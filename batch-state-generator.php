<?php
/**
 * Batch State Page Generator
 * Creates multiple state pages at once using the Alabama template
 */

// Set content type for web output
if (isset($_SERVER['HTTP_HOST'])) {
    header('Content-Type: text/plain; charset=utf-8');
}

echo "🚀 Batch State Page Generator Starting...\n\n";

// Define all US states to create (excluding Alabama which already exists)
$all_states = array(
    'California', 'Texas', 'Florida', 'New York', 'Pennsylvania',
    'Illinois', 'Ohio', 'Georgia', 'North Carolina', 'Michigan',
    'New Jersey', 'Virginia', 'Washington', 'Arizona', 'Massachusetts',
    'Tennessee', 'Indiana', 'Missouri', 'Maryland', 'Wisconsin',
    'Colorado', 'Minnesota', 'South Carolina', 'Louisiana', 'Kentucky',
    'Oregon', 'Oklahoma', 'Connecticut', 'Utah', 'Iowa',
    'Nevada', 'Arkansas', 'Mississippi', 'Kansas', 'New Mexico',
    'Nebraska', 'West Virginia', 'Idaho', 'Hawaii', 'New Hampshire',
    'Maine', 'Montana', 'Rhode Island', 'Delaware', 'South Dakota',
    'North Dakota', 'Alaska', 'Vermont', 'Wyoming', 'District of Columbia'
);

// Read the Alabama template
$alabama_file = __DIR__ . '/wp-content/themes/generatepress-child/page-alabama.php';

if (!file_exists($alabama_file)) {
    die("❌ Error: Alabama template not found at: $alabama_file\n");
}

$template_content = file_get_contents($alabama_file);
echo "✅ Alabama template loaded (" . strlen($template_content) . " characters)\n";
echo "📋 Will create " . count($all_states) . " state pages\n\n";

// Helper functions
function state_to_slug($state) {
    return strtolower(str_replace(array(' ', '.'), array('-', ''), $state));
}

function state_to_var($state) {
    return strtolower(str_replace(array(' ', '.', '-'), '_', $state));
}

function state_to_camel($state) {
    return str_replace(array(' ', '-', '.'), '', ucwords($state, ' -.'));
}

function get_major_cities($state) {
    $city_map = array(
        'California' => array('Los Angeles', 'San Francisco', 'San Diego', 'Sacramento'),
        'Texas' => array('Houston', 'Dallas', 'Austin', 'San Antonio'),
        'Florida' => array('Miami', 'Orlando', 'Tampa', 'Jacksonville'),
        'New York' => array('New York City', 'Buffalo', 'Rochester', 'Syracuse'),
        'Pennsylvania' => array('Philadelphia', 'Pittsburgh', 'Allentown', 'Erie'),
        'Illinois' => array('Chicago', 'Aurora', 'Rockford', 'Joliet'),
        'Ohio' => array('Columbus', 'Cleveland', 'Cincinnati', 'Toledo'),
        'Georgia' => array('Atlanta', 'Augusta', 'Columbus', 'Savannah'),
        'North Carolina' => array('Charlotte', 'Raleigh', 'Greensboro', 'Durham'),
        'Michigan' => array('Detroit', 'Grand Rapids', 'Warren', 'Sterling Heights'),
    );
    
    return isset($city_map[$state]) ? $city_map[$state] : array('City1', 'City2', 'City3', 'City4');
}

// Process each state
$created_count = 0;
$skipped_count = 0;

foreach ($all_states as $state) {
    $state_slug = state_to_slug($state);
    $state_var = state_to_var($state);
    $state_camel = state_to_camel($state);
    $major_cities = get_major_cities($state);
    
    $filename = "page-{$state_slug}.php";
    $filepath = __DIR__ . "/wp-content/themes/generatepress-child/{$filename}";
    
    // Check if file already exists
    if (file_exists($filepath)) {
        echo "⏭️  Skipping $state - file already exists: $filename\n";
        $skipped_count++;
        continue;
    }
    
    echo "🔨 Creating: $state -> $filename\n";
    
    // Create state-specific content
    $state_content = $template_content;
    
    // Replace template header
    $state_content = str_replace(
        'Template Name: Alabama State Page',
        "Template Name: {$state} State Page",
        $state_content
    );
    
    $state_content = str_replace(
        'Custom Page Template for Alabama State Page',
        "Custom Page Template for {$state} State Page",
        $state_content
    );
    
    // Replace Google Maps callback
    $state_content = str_replace(
        'callback=initAlabamaZoosMap',
        "callback=init{$state_camel}ZoosMap",
        $state_content
    );
    
    // Replace state term variable
    $state_content = str_replace(
        '$alabama_term = get_term_by(\'slug\', \'alabama\', \'location\');',
        "\${$state_var}_term = get_term_by('slug', '{$state_slug}', 'location');",
        $state_content
    );
    
    $state_content = str_replace(
        '$zoo_count = $alabama_term ? $alabama_term->count : 5;',
        "\$zoo_count = \${$state_var}_term ? \${$state_var}_term->count : 5;",
        $state_content
    );
    
    // Replace breadcrumbs
    $state_content = str_replace(
        '<span class="current">Alabama</span>',
        "<span class=\"current\">{$state}</span>",
        $state_content
    );
    
    // Replace page title
    $state_content = str_replace(
        'Best Petting Zoos in Alabama',
        "Best Petting Zoos in {$state}",
        $state_content
    );
    
    // Replace city names in content
    $alabama_cities = array('Birmingham', 'Huntsville', 'Mobile', 'Montgomery');
    $state_content = str_replace(
        '$city_names = array(\'Birmingham\', \'Huntsville\', \'Mobile\', \'Montgomery\');',
        '$city_names = array(\'' . implode('\', \'', $major_cities) . '\');',
        $state_content
    );
    
    // Replace all section titles and content
    $replacements = array(
        'All Petting Zoos in Alabama' => "All Petting Zoos in {$state}",
        'Best Petting Zoos in Alabama by Ranking' => "Best Petting Zoos in {$state} by Ranking",
        'Best Petting Zoos for Birthday Parties in Alabama' => "Best Petting Zoos for Birthday Parties in {$state}",
        'Best Mobile Petting Zoos in Alabama' => "Best Mobile Petting Zoos in {$state}",
        'Best Petting Zoos in Alabama with Bunnies' => "Best Petting Zoos in {$state} with Bunnies",
        'Best Indoor Petting Zoos in Alabama' => "Best Indoor Petting Zoos in {$state}",
        'Other Cities in Alabama' => "Other Cities in {$state}",
        'across Alabama' => "across {$state}",
        'in Alabama' => "in {$state}",
        'Alabama petting zoos' => "{$state} petting zoos",
        'throughout Alabama' => "throughout {$state}",
        'across the state' => "across {$state}",
        'Find Your Perfect Petting Zoo Experience in Alabama' => "Find Your Perfect Petting Zoo Experience in {$state}",
    );
    
    foreach ($replacements as $search => $replace) {
        $state_content = str_replace($search, $replace, $state_content);
    }
    
    // Replace map container ID and function names
    $state_content = str_replace(
        'id="alabama-zoos-map"',
        "id=\"{$state_slug}-zoos-map\"",
        $state_content
    );
    
    $state_content = str_replace(
        'initAlabamaZoosMap',
        "init{$state_camel}ZoosMap",
        $state_content
    );
    
    $state_content = str_replace(
        'id="all-alabama-zoos"',
        "id=\"all-{$state_slug}-zoos\"",
        $state_content
    );
    
    // Replace console log messages
    $log_replacements = array(
        'Alabama page content loaded' => "{$state} page content loaded",
        'Loading All Alabama Zoos section' => "Loading All {$state} Zoos section",
        'Loading Other Cities in Alabama section' => "Loading Other Cities in {$state} section",
        'Alabama Page:' => "{$state} Page:",
    );
    
    foreach ($log_replacements as $search => $replace) {
        $state_content = str_replace($search, $replace, $state_content);
    }
    
    // Replace FAQ content
    $faq_replacements = array(
        'What are the most popular petting zoos in Alabama?' => "What are the most popular petting zoos in {$state}?",
        'Some of the most popular petting zoos in Alabama include' => "Some of the most popular petting zoos in {$state} include",
        'Are petting zoos in Alabama suitable for children' => "Are petting zoos in {$state} suitable for children",
        'petting zoos in Alabama are suitable for children' => "petting zoos in {$state} are suitable for children",
        'What should I bring to a petting zoo in Alabama?' => "What should I bring to a petting zoo in {$state}?",
        'When visiting a petting zoo in Alabama' => "When visiting a petting zoo in {$state}",
    );
    
    foreach ($faq_replacements as $search => $replace) {
        $state_content = str_replace($search, $replace, $state_content);
    }
    
    // Replace taxonomy terms
    $state_content = str_replace(
        "'terms' => 'alabama',",
        "'terms' => '{$state_slug}',",
        $state_content
    );
    
    // Replace remaining alabama/Alabama references
    $state_content = str_replace('alabama', $state_slug, $state_content);
    $state_content = str_replace('Alabama', $state, $state_content);
    
    // Write the file
    $result = file_put_contents($filepath, $state_content);
    
    if ($result !== false) {
        echo "✅ Created: $filename (" . number_format($result) . " bytes)\n";
        $created_count++;
    } else {
        echo "❌ Failed to create: $filename\n";
    }
}

echo "\n🎉 Batch Generation Complete!\n";
echo "📊 Summary:\n";
echo "   - Created: $created_count pages\n";
echo "   - Skipped: $skipped_count pages (already existed)\n";
echo "   - Total states processed: " . count($all_states) . "\n";

if ($created_count > 0) {
    echo "\n🔧 Generated files (first 10):\n";
    $count = 0;
    foreach ($all_states as $state) {
        if ($count >= 10) break;
        $state_slug = state_to_slug($state);
        $filename = "page-{$state_slug}.php";
        $filepath = __DIR__ . "/wp-content/themes/generatepress-child/{$filename}";
        
        if (file_exists($filepath)) {
            echo "   - $filename\n";
            $count++;
        }
    }
    
    if ($created_count > 10) {
        echo "   ... and " . ($created_count - 10) . " more files\n";
    }
}

echo "\n💡 Next steps:\n";
echo "   1. Review the generated pages in wp-content/themes/generatepress-child/\n";
echo "   2. Test a few pages to ensure proper functionality\n";
echo "   3. Check console logs for any errors\n";
echo "   4. Verify Google Maps integration works for each state\n";
echo "\n✨ All state pages are ready to use!\n";
?>
