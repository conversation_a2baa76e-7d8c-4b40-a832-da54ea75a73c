<?php
/**
 * State Page Generator Script
 * Generates individual state page templates based on the Alabama template
 * Run this script to create all state pages with proper structure and styling
 */

// Set content type for web output
if (isset($_SERVER['HTTP_HOST'])) {
    header('Content-Type: text/plain; charset=utf-8');
}

// Prevent direct access
if (!defined('ABSPATH')) {
    // For standalone execution
    define('ABSPATH', dirname(__FILE__) . '/');
}

echo "🚀 Starting State Page Generator...\n";

// Read CSV file to get all states
$csv_file = ABSPATH . 'wp-content/uploads/2025/06/city-state.csv';
$states = array();

if (!file_exists($csv_file)) {
    die("❌ Error: CSV file not found at: $csv_file\n");
}

echo "📊 Reading CSV file: $csv_file\n";

$handle = fopen($csv_file, 'r');
if ($handle !== FALSE) {
    $header = fgetcsv($handle); // Skip header row
    echo "📋 CSV Header: " . implode(', ', $header) . "\n";

    while (($data = fgetcsv($handle)) !== FALSE) {
        if (count($data) >= 3 && !empty($data[2])) {
            $state = trim($data[2]);
            if (!in_array($state, $states)) {
                $states[] = $state;
            }
        }
    }
    fclose($handle);
}

// Remove Alabama since it already exists
$states = array_filter($states, function($state) {
    return $state !== 'Alabama';
});

sort($states);

echo "📋 Found " . count($states) . " states to process (excluding Alabama)\n";
echo "States: " . implode(', ', array_slice($states, 0, 10)) . (count($states) > 10 ? '...' : '') . "\n\n";

// Read the Alabama template
$alabama_template = ABSPATH . 'wp-content/themes/generatepress-child/page-alabama.php';
if (!file_exists($alabama_template)) {
    die("❌ Error: Alabama template not found at: $alabama_template\n");
}

$template_content = file_get_contents($alabama_template);
echo "✅ Alabama template loaded (" . strlen($template_content) . " characters)\n";

// Function to convert state name to slug
function state_to_slug($state) {
    return strtolower(str_replace(array(' ', '.'), array('-', ''), $state));
}

// Function to convert state name to variable name
function state_to_var($state) {
    return strtolower(str_replace(array(' ', '.', '-'), '_', $state));
}

// Function to get state abbreviation (simplified mapping)
function get_state_abbr($state) {
    $state_abbrevs = array(
        'California' => 'CA', 'Texas' => 'TX', 'Florida' => 'FL', 'New York' => 'NY',
        'Pennsylvania' => 'PA', 'Illinois' => 'IL', 'Ohio' => 'OH', 'Georgia' => 'GA',
        'North Carolina' => 'NC', 'Michigan' => 'MI', 'New Jersey' => 'NJ', 'Virginia' => 'VA',
        'Washington' => 'WA', 'Arizona' => 'AZ', 'Massachusetts' => 'MA', 'Tennessee' => 'TN',
        'Indiana' => 'IN', 'Missouri' => 'MO', 'Maryland' => 'MD', 'Wisconsin' => 'WI',
        'Colorado' => 'CO', 'Minnesota' => 'MN', 'South Carolina' => 'SC', 'Louisiana' => 'LA',
        'Kentucky' => 'KY', 'Oregon' => 'OR', 'Oklahoma' => 'OK', 'Connecticut' => 'CT',
        'Utah' => 'UT', 'Iowa' => 'IA', 'Nevada' => 'NV', 'Arkansas' => 'AR',
        'Mississippi' => 'MS', 'Kansas' => 'KS', 'New Mexico' => 'NM', 'Nebraska' => 'NE',
        'West Virginia' => 'WV', 'Idaho' => 'ID', 'Hawaii' => 'HI', 'New Hampshire' => 'NH',
        'Maine' => 'ME', 'Montana' => 'MT', 'Rhode Island' => 'RI', 'Delaware' => 'DE',
        'South Dakota' => 'SD', 'North Dakota' => 'ND', 'Alaska' => 'AK', 'Vermont' => 'VT',
        'Wyoming' => 'WY', 'District of Columbia' => 'DC', 'Puerto Rico' => 'PR'
    );
    
    return isset($state_abbrevs[$state]) ? $state_abbrevs[$state] : strtoupper(substr($state, 0, 2));
}

// Process each state
$created_count = 0;
$skipped_count = 0;

foreach ($states as $state) {
    $state_slug = state_to_slug($state);
    $state_var = state_to_var($state);
    $state_abbr = get_state_abbr($state);
    
    $filename = "page-{$state_slug}.php";
    $filepath = ABSPATH . "wp-content/themes/generatepress-child/{$filename}";
    
    // Check if file already exists
    if (file_exists($filepath)) {
        echo "⏭️  Skipping $state - file already exists: $filename\n";
        $skipped_count++;
        continue;
    }
    
    echo "🔨 Creating page for: $state ($state_abbr) -> $filename\n";
    
    // Create state-specific content by replacing Alabama references
    $state_content = $template_content;
    
    // Replace template name and description
    $state_content = str_replace(
        'Template Name: Alabama State Page',
        "Template Name: {$state} State Page",
        $state_content
    );
    
    $state_content = str_replace(
        'Custom Page Template for Alabama State Page',
        "Custom Page Template for {$state} State Page",
        $state_content
    );
    
    // Replace Google Maps callback function
    $state_content = str_replace(
        'callback=initAlabamaZoosMap',
        "callback=init{$state_var}ZoosMap",
        $state_content
    );
    
    // Replace state term variable
    $state_content = str_replace(
        '$alabama_term = get_term_by(\'slug\', \'alabama\', \'location\');',
        "\${$state_var}_term = get_term_by('slug', '{$state_slug}', 'location');",
        $state_content
    );
    
    $state_content = str_replace(
        '$zoo_count = $alabama_term ? $alabama_term->count : 5;',
        "\$zoo_count = \${$state_var}_term ? \${$state_var}_term->count : 5;",
        $state_content
    );
    
    // Replace breadcrumbs
    $state_content = str_replace(
        '<span class="current">Alabama</span>',
        "<span class=\"current\">{$state}</span>",
        $state_content
    );
    
    // Replace page title
    $state_content = str_replace(
        'Best Petting Zoos in Alabama',
        "Best Petting Zoos in {$state}",
        $state_content
    );
    
    // Replace subtitle
    $state_content = str_replace(
        'Explore <?php echo $zoo_count; ?> petting zoos across Alabama',
        "Explore <?php echo \$zoo_count; ?> petting zoos across {$state}",
        $state_content
    );
    
    // Replace all Alabama references in content
    $state_content = str_replace('alabama', $state_slug, $state_content);
    $state_content = str_replace('Alabama', $state, $state_content);

    // Replace specific section titles and content
    $state_content = str_replace(
        'All Petting Zoos in Alabama',
        "All Petting Zoos in {$state}",
        $state_content
    );

    $state_content = str_replace(
        'Best Petting Zoos in Alabama by Ranking',
        "Best Petting Zoos in {$state} by Ranking",
        $state_content
    );

    $state_content = str_replace(
        'Best Petting Zoos for Birthday Parties in Alabama',
        "Best Petting Zoos for Birthday Parties in {$state}",
        $state_content
    );

    $state_content = str_replace(
        'Best Mobile Petting Zoos in Alabama',
        "Best Mobile Petting Zoos in {$state}",
        $state_content
    );

    $state_content = str_replace(
        'Best Petting Zoos in Alabama with Bunnies',
        "Best Petting Zoos in {$state} with Bunnies",
        $state_content
    );

    $state_content = str_replace(
        'Best Indoor Petting Zoos in Alabama',
        "Best Indoor Petting Zoos in {$state}",
        $state_content
    );

    $state_content = str_replace(
        'Other Cities in Alabama',
        "Other Cities in {$state}",
        $state_content
    );

    // Replace content descriptions
    $state_content = str_replace(
        'across Alabama',
        "across {$state}",
        $state_content
    );

    $state_content = str_replace(
        'in Alabama',
        "in {$state}",
        $state_content
    );

    $state_content = str_replace(
        'Alabama petting zoos',
        "{$state} petting zoos",
        $state_content
    );

    $state_content = str_replace(
        'throughout Alabama',
        "throughout {$state}",
        $state_content
    );

    $state_content = str_replace(
        'across the state',
        "across {$state}",
        $state_content
    );

    // Replace map container ID and function names
    $state_content = str_replace(
        'id="alabama-zoos-map"',
        "id=\"{$state_slug}-zoos-map\"",
        $state_content
    );

    $state_content = str_replace(
        'initAlabamaZoosMap',
        "init" . str_replace(array(' ', '-'), '', ucwords($state_slug, '-')) . "ZoosMap",
        $state_content
    );

    // Replace console log messages
    $state_content = str_replace(
        'Alabama page content loaded',
        "{$state} page content loaded",
        $state_content
    );

    $state_content = str_replace(
        'Loading All Alabama Zoos section',
        "Loading All {$state} Zoos section",
        $state_content
    );

    $state_content = str_replace(
        'Loading Other Cities in Alabama section',
        "Loading Other Cities in {$state} section",
        $state_content
    );

    $state_content = str_replace(
        'Alabama Page:',
        "{$state} Page:",
        $state_content
    );

    // Replace FAQ content
    $state_content = str_replace(
        'What are the most popular petting zoos in Alabama?',
        "What are the most popular petting zoos in {$state}?",
        $state_content
    );

    $state_content = str_replace(
        'Some of the most popular petting zoos in Alabama include',
        "Some of the most popular petting zoos in {$state} include",
        $state_content
    );

    $state_content = str_replace(
        'Are petting zoos in Alabama suitable for children',
        "Are petting zoos in {$state} suitable for children",
        $state_content
    );

    $state_content = str_replace(
        'petting zoos in Alabama are suitable for children',
        "petting zoos in {$state} are suitable for children",
        $state_content
    );

    $state_content = str_replace(
        'What should I bring to a petting zoo in Alabama?',
        "What should I bring to a petting zoo in {$state}?",
        $state_content
    );

    $state_content = str_replace(
        'When visiting a petting zoo in Alabama',
        "When visiting a petting zoo in {$state}",
        $state_content
    );

    // Write the file
    $result = file_put_contents($filepath, $state_content);

    if ($result !== false) {
        echo "✅ Created: $filename (" . number_format($result) . " bytes)\n";
        $created_count++;
    } else {
        echo "❌ Failed to create: $filename\n";
    }
}

echo "\n🎉 State Page Generation Complete!\n";
echo "📊 Summary:\n";
echo "   - Created: $created_count pages\n";
echo "   - Skipped: $skipped_count pages (already existed)\n";
echo "   - Total states processed: " . count($states) . "\n";
echo "\n💡 Next steps:\n";
echo "   1. Review the generated pages in wp-content/themes/generatepress-child/\n";
echo "   2. Test a few pages to ensure proper functionality\n";
echo "   3. Check console logs for any errors\n";
echo "   4. Verify Google Maps integration works for each state\n";

if ($created_count > 0) {
    echo "\n🔧 Generated files:\n";
    foreach ($states as $state) {
        $state_slug = state_to_slug($state);
        $filename = "page-{$state_slug}.php";
        $filepath = ABSPATH . "wp-content/themes/generatepress-child/{$filename}";
        
        if (file_exists($filepath)) {
            echo "   - $filename\n";
        }
    }
}

echo "\n✨ All done! Your state pages are ready to use.\n";
?>
