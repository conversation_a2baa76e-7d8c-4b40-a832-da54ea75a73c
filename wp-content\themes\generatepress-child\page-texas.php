<?php
/**
 * Custom Page Template for Texas State Page
 * Template Name: Texas State Page
 */

get_header();

// Add Google Maps API script - Replace YOUR_API_KEY with your actual Google Maps API key
wp_enqueue_script('google-maps', 'https://maps.googleapis.com/maps/api/js?key=AIzaSyDSY9K8KUaLvpm42ubwTCU3jJKtzZ8GXmA&callback=initTexasZoosMap', array(), null, true);

// Get Texas location term for data
$texas_term = get_term_by('slug', 'texas', 'location');
$zoo_count = $texas_term ? $texas_term->count : 5; // fallback to 5 if term not found
?>

<div id="primary" class="content-area full-width-content">
    <main id="main" class="site-main">

        <!-- Enhanced Page Header - Zoo Style -->
        <div class="city-hero-section">
            <div class="hero-overlay"></div>
            <div class="hero-content-wrapper">
                <div class="hero-content">
                    <div class="breadcrumbs text-center">
                        <a href="<?php echo home_url(); ?>">Home</a>
                        <span class="separator">›</span>
                        <span class="current">Texas</span>
                    </div>

                    <h1 class="city-title">
                        Best Petting Zoos in Texas
                    </h1>

                    <p class="hero-subtitle text-center">
                        Explore <?php echo $zoo_count; ?> petting zoos across Texas
                    </p>
                </div>
            </div>
        </div>

        <!-- Main Content Container -->
        <div class="container" style="max-width: 1400px; margin: 0 auto; padding: 0 2rem; text-align: center;">

            <!-- Original Page Content -->
            <?php while (have_posts()) : the_post(); ?>
                <div class="original-content" style="text-align: left; margin-bottom: 4rem;">
                    <?php
                    // Get the content and remove the title since we're using it in the hero
                    $content = get_the_content();
                    $content = apply_filters('the_content', $content);

                    // Only remove the specific FAQ section at the end to avoid duplicates
                    // More precise regex that only matches the FAQ section with its content
                    $content = preg_replace('/<h2[^>]*>\s*frequently asked questions\s*<\/h2>.*?<p>.*?petting zoos.*?<\/a>\.<\/p>/is', '', $content);

                    // Convert city names from H2 to H3 under Popular Cities section
                    $city_names = array('Houston', 'Dallas', 'Austin', 'San Antonio');
                    foreach ($city_names as $city) {
                        $content = preg_replace('/<h2([^>]*)>\s*' . preg_quote($city, '/') . '\s*<\/h2>/i', '<h3$1>' . $city . '</h3>', $content);
                    }

                    // Debug: Log what content we're displaying
                    echo '<!-- DEBUG: Original content length: ' . strlen($content) . ' characters -->';
                    echo '<!-- DEBUG: Content preview: ' . substr(strip_tags($content), 0, 200) . '... -->';
                    echo '<!-- DEBUG: Full content (first 500 chars): ' . substr($content, 0, 500) . ' -->';
                    echo '<!-- DEBUG: City heading conversions applied for: ' . implode(', ', $city_names) . ' -->';

                    echo $content;

                    // Add JavaScript console logging for content debugging
                    echo '<script>console.log("🔍 Texas page content loaded. Content length: ' . strlen($content) . ' characters");</script>';
                    echo '<script>console.log("🏙️ City headings converted to H3: ' . implode(', ', $city_names) . '");</script>';
                    ?>
                </div>
            <?php endwhile; ?>

        </div>

        <!-- New SEO-Focused H2 Sections -->
        <div class="container" style="max-width: 1400px; margin: 0 auto; padding: 0 2rem;">

            <!-- All Petting Zoos in Texas - Moved up from bottom -->
            <section class="all-zoos-section">
                <h2>All Petting Zoos in Texas</h2>
                <p>Discover all the amazing petting zoos across Texas. Use our interactive map and filters to find the perfect family destination near you.</p>

                <!-- Filter Section -->
                <div class="filter-section" style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 30px 0;">
                    <div style="display: flex; align-items: center; justify-content: center; margin-bottom: 20px;">
                        <span style="font-size: 18px; margin-right: 15px;">🔍</span>
                        <h3 style="margin: 0; color: #2c5530;">Find Your Perfect Petting Zoo Experience in Texas</h3>
                    </div>

                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 20px;">
                        <!-- Animal Types Filter -->
                        <div>
                            <label for="animal-filter" style="display: block; margin-bottom: 5px; font-weight: 600; color: #2c5530;">🐰 Animal Types:</label>
                            <select id="animal-filter" style="width: 100%; padding: 8px; border: 2px solid #ddd; border-radius: 6px; font-size: 14px;">
                                <option value="">All Animals</option>
                                <?php
                                // Get all animal types
                                $animal_types = get_terms(array(
                                    'taxonomy' => 'animal_type',
                                    'hide_empty' => false,
                                    'orderby' => 'name',
                                    'order' => 'ASC'
                                ));

                                if (!empty($animal_types) && !is_wp_error($animal_types)) {
                                    foreach ($animal_types as $animal_type) {
                                        echo '<option value="' . esc_attr($animal_type->slug) . '">' . esc_html($animal_type->name) . '</option>';
                                    }
                                } else {
                                    // Fallback options if no terms in database
                                    echo '<option value="goats">Goats</option>';
                                    echo '<option value="sheep">Sheep</option>';
                                    echo '<option value="rabbits">Rabbits</option>';
                                    echo '<option value="chickens">Chickens</option>';
                                    echo '<option value="pigs">Pigs</option>';
                                    echo '<option value="horses">Horses</option>';
                                    echo '<option value="llamas">Llamas</option>';
                                    echo '<option value="alpacas">Alpacas</option>';
                                }
                                ?>
                            </select>
                        </div>

                        <!-- Zoo Types Filter -->
                        <div>
                            <label for="zoo-type-filter" style="display: block; margin-bottom: 5px; font-weight: 600; color: #2c5530;">🏡 Zoo Types:</label>
                            <select id="zoo-type-filter" style="width: 100%; padding: 8px; border: 2px solid #ddd; border-radius: 6px; font-size: 14px;">
                                <option value="">All Types</option>
                                <?php
                                // Get all zoo types
                                $zoo_types = get_terms(array(
                                    'taxonomy' => 'zoo_type',
                                    'hide_empty' => false,
                                    'orderby' => 'name',
                                    'order' => 'ASC'
                                ));

                                if (!empty($zoo_types) && !is_wp_error($zoo_types)) {
                                    foreach ($zoo_types as $zoo_type) {
                                        echo '<option value="' . esc_attr($zoo_type->slug) . '">' . esc_html($zoo_type->name) . '</option>';
                                    }
                                } else {
                                    // Fallback options if no terms in database
                                    echo '<option value="farm">Farm</option>';
                                    echo '<option value="mobile">Mobile</option>';
                                    echo '<option value="indoor">Indoor</option>';
                                    echo '<option value="outdoor">Outdoor</option>';
                                    echo '<option value="educational">Educational</option>';
                                }
                                ?>
                            </select>
                        </div>

                        <!-- Features Filter -->
                        <div>
                            <label for="features-filter" style="display: block; margin-bottom: 5px; font-weight: 600; color: #2c5530;">✨ Features:</label>
                            <select id="features-filter" style="width: 100%; padding: 8px; border: 2px solid #ddd; border-radius: 6px; font-size: 14px;">
                                <option value="">All Features</option>
                                <?php
                                // Get all features
                                $features = get_terms(array(
                                    'taxonomy' => 'features',
                                    'hide_empty' => false,
                                    'orderby' => 'name',
                                    'order' => 'ASC'
                                ));

                                if (!empty($features) && !is_wp_error($features)) {
                                    foreach ($features as $feature) {
                                        echo '<option value="' . esc_attr($feature->slug) . '">' . esc_html($feature->name) . '</option>';
                                    }
                                } else {
                                    // Fallback options if no terms in database
                                    echo '<option value="birthday-parties">Birthday Parties</option>';
                                    echo '<option value="educational-programs">Educational Programs</option>';
                                    echo '<option value="wheelchair-accessible">Wheelchair Accessible</option>';
                                    echo '<option value="parking">Parking Available</option>';
                                    echo '<option value="restrooms">Restrooms</option>';
                                    echo '<option value="picnic-areas">Picnic Areas</option>';
                                }
                                ?>
                            </select>
                        </div>
                    </div>

                    <div style="text-align: center;">
                        <button id="clear-filters" style="background: #6c757d; color: white; border: none; padding: 10px 20px; border-radius: 6px; cursor: pointer; font-size: 14px;">
                            Clear All Filters
                        </button>
                    </div>
                </div>

                <!-- Google Maps -->
                <div class="map-container" style="margin: 30px 0;">
                    <div id="texas-zoos-map" style="height: 400px; width: 100%; border-radius: 8px;"></div>
                </div>

                <!-- Petting Zoo Cards Grid -->
                <div class="petting-zoo-cards-grid" id="all-texas-zoos">
                    <?php
                    // Get all Texas petting zoos (max 9 for 3 rows)
                    $all_zoos_args = array(
                        'post_type' => 'petting_zoo',
                        'posts_per_page' => 9,
                        'tax_query' => array(
                            array(
                                'taxonomy' => 'location',
                                'field' => 'slug',
                                'terms' => 'texas',
                            )
                        ),
                        'meta_key' => 'zoo_data',
                        'orderby' => 'meta_value',
                        'order' => 'DESC'
                    );

                    $all_zoos_query = new WP_Query($all_zoos_args);
                    $map_locations = array(); // For Google Maps

                    echo '<script>console.log("🗺️ Loading All Texas Zoos section with ' . $all_zoos_query->found_posts . ' zoos");</script>';

                    if ($all_zoos_query->have_posts()) :
                        $card_count = 0;
                        while ($all_zoos_query->have_posts() && $card_count < 9) : $all_zoos_query->the_post();
                            $card_count++;
                            // Get zoo data for consistent formatting
                            $zoo_data = get_post_meta(get_the_ID(), 'zoo_data', true);
                            $json_data = get_post_meta(get_the_ID(), '_petting_zoo_json_data', true);

                            // Parse JSON data for pics and address
                            $pics = array();
                            $address = array();
                            if ($json_data) {
                                $data = json_decode($json_data, true);
                                if (isset($data['pics']) && is_array($data['pics'])) {
                                    $pics = $data['pics'];
                                }
                                if (isset($data['address'])) {
                                    $address = $data['address'];
                                }
                            }

                            // Build full address string
                            $full_address = '';
                            if (!empty($address)) {
                                $address_parts = array();
                                if (!empty($address['street'])) $address_parts[] = $address['street'];
                                if (!empty($address['city'])) $address_parts[] = $address['city'];
                                if (!empty($address['state'])) $address_parts[] = $address['state'];
                                if (!empty($address['zip'])) $address_parts[] = $address['zip'];
                                $full_address = implode(', ', $address_parts);
                            }

                            // Debug logging for address
                            echo '<!-- DEBUG ALL ZOOS CARD: Zoo=' . get_the_title() . ' | Address Data=' . json_encode($address) . ' | Full Address=' . $full_address . ' -->';
                            if (empty($full_address)) {
                                echo '<!-- DEBUG: No address found for ' . get_the_title() . ' -->';
                            }

                            // Get first picture
                            $first_pic = '';
                            if (!empty($pics) && isset($pics[0])) {
                                $pic_path = $pics[0];
                                // Convert Windows path to URL
                                if (strpos($pic_path, 'C:\\') === 0) {
                                    $relative_path = str_replace('C:\\Users\\<USER>\\Downloads\\localsites\\pettingzoo\\app\\public\\wp-content\\uploads\\', '', $pic_path);
                                    $relative_path = str_replace('\\', '/', $relative_path);
                                    $first_pic = wp_upload_dir()['baseurl'] . '/' . $relative_path;
                                } else {
                                    $first_pic = wp_upload_dir()['baseurl'] . '/' . ltrim($pic_path, '/');
                                }
                            }

                            // Fallback to placeholder if no pic
                            if (empty($first_pic)) {
                                $first_pic = wp_upload_dir()['baseurl'] . '/2025/06/pettingzoophoto%20(1).webp';
                            }

                            // Get rating from JSON data
                            $rating = '';
                            if ($json_data) {
                                $data = json_decode($json_data, true);
                                if (isset($data['rating'])) {
                                    $rating = $data['rating'];
                                }
                            }

                            // Get features for filtering
                            $zoo_features = get_the_terms(get_the_ID(), 'features');
                            $animal_types = get_the_terms(get_the_ID(), 'animal_type');
                            $zoo_types = get_the_terms(get_the_ID(), 'zoo_type');

                            // Build filter classes
                            $filter_classes = array();
                            if ($zoo_features && !is_wp_error($zoo_features)) {
                                foreach ($zoo_features as $feature) {
                                    $filter_classes[] = 'feature-' . $feature->slug;
                                }
                            }

                            $animal_slugs = array();
                            if ($animal_types && !is_wp_error($animal_types)) {
                                foreach ($animal_types as $animal) {
                                    $animal_slugs[] = 'animal-' . $animal->slug;
                                }
                            }

                            $zoo_type_slugs = array();
                            if ($zoo_types && !is_wp_error($zoo_types)) {
                                foreach ($zoo_types as $zoo_type) {
                                    $zoo_type_slugs[] = 'zoo-type-' . $zoo_type->slug;
                                }
                            }

                            // Add to map locations
                            if (!empty($address['lat']) && !empty($address['lng'])) {
                                $map_locations[] = array(
                                    'lat' => floatval($address['lat']),
                                    'lng' => floatval($address['lng']),
                                    'title' => get_the_title(),
                                    'address' => $full_address,
                                    'url' => get_permalink()
                                );
                            }
                            ?>
                            <!-- DEBUG: Card data - Animals: <?php echo implode(', ', $animal_slugs); ?> | Zoo Types: <?php echo implode(', ', $zoo_type_slugs); ?> | Features: <?php echo implode(', ', $filter_classes); ?> -->
                            <div class="petting-zoo-card"
                                 data-filters="<?php echo implode(' ', $filter_classes); ?>"
                                 data-animals="<?php echo implode(' ', $animal_slugs); ?>"
                                 data-zoo-types="<?php echo implode(' ', $zoo_type_slugs); ?>">
                                <div class="card-image">
                                    <a href="<?php the_permalink(); ?>">
                                        <?php if (has_post_thumbnail()) : ?>
                                            <?php the_post_thumbnail('medium', array('alt' => get_the_title(), 'loading' => 'lazy')); ?>
                                        <?php else : ?>
                                            <img src="<?php echo wp_upload_dir()['baseurl']; ?>/2025/06/pettingzoophoto%20(1).webp" alt="<?php the_title(); ?>" loading="lazy">
                                        <?php endif; ?>
                                    </a>
                                    <?php if ($rating) : ?>
                                        <div class="rating-badge">
                                            <span class="rating-number"><?php echo esc_html($rating); ?></span>
                                            <span class="rating-star">★</span>
                                        </div>
                                    <?php endif; ?>
                                </div>

                                <div class="card-content">
                                    <h3 class="zoo-name text-center">
                                        <?php the_title(); ?>
                                    </h3>

                                    <?php if ($full_address) : ?>
                                        <div class="zoo-location text-center" style="margin: 8px 0; color: #666; font-size: 14px;">
                                            <svg width="12" height="12" viewBox="0 0 24 24" fill="currentColor" style="margin-right: 4px; vertical-align: middle;">
                                                <path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"/>
                                            </svg>
                                            <?php echo esc_html($full_address); ?>
                                        </div>
                                    <?php else: ?>
                                        <!-- DEBUG: No address to display for <?php echo get_the_title(); ?> -->
                                    <?php endif; ?>

                                    <div class="activities-section">
                                        <?php if ($zoo_features && !is_wp_error($zoo_features)) : ?>
                                            <div class="activity-tags">
                                                <?php
                                                $feature_count = 0;
                                                foreach ($zoo_features as $feature) :
                                                    if ($feature_count >= 4) break; // Show up to 4 features
                                                    ?>
                                                    <span class="activity-tag"><?php echo esc_html($feature->name); ?></span>
                                                    <?php
                                                    $feature_count++;
                                                endforeach;

                                                // Show +X more if there are additional features
                                                $remaining_features = count($zoo_features) - $feature_count;
                                                if ($remaining_features > 0) : ?>
                                                    <span class="activity-tag more-tag">+<?php echo $remaining_features; ?> more</span>
                                                <?php endif; ?>
                                            </div>
                                        <?php endif; ?>

                                        <div class="zoo-description">
                                            <?php
                                            // Get first 10 words from content
                                            $post_content = get_the_content();
                                            $post_content = wp_strip_all_tags($post_content);
                                            $first_10_words = wp_trim_words($post_content, 10, '...');
                                            echo esc_html($first_10_words);
                                            ?>
                                        </div>
                                    </div>

                                    <div class="card-actions">
                                        <a href="<?php the_permalink(); ?>" class="btn btn-primary">View Details →</a>
                                    </div>
                                </div>
                            </div>
                            <?php
                        endwhile;
                        wp_reset_postdata();
                    else : ?>
                        <div class="no-results">
                            <p>No petting zoos found in Texas. Please check back later!</p>
                        </div>
                    <?php endif; ?>
                </div>
