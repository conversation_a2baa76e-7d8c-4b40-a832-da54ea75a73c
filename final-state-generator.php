<?php
/**
 * Final State Page Generator
 * Complete solution for creating all remaining state pages
 */

// Set content type for web output
if (isset($_SERVER['HTTP_HOST'])) {
    header('Content-Type: text/plain; charset=utf-8');
}

echo "🎯 Final State Page Generator\n";
echo "============================\n\n";

// Define all remaining states to create
$remaining_states = array(
    'Florida' => array('Miami', 'Orlando', 'Tampa', 'Jacksonville'),
    'New York' => array('New York City', 'Buffalo', 'Rochester', 'Syracuse'),
    'Pennsylvania' => array('Philadelphia', 'Pittsburgh', 'Allentown', 'Erie'),
    'Illinois' => array('Chicago', 'Aurora', 'Rockford', 'Joliet'),
    'Ohio' => array('Columbus', 'Cleveland', 'Cincinnati', 'Toledo'),
    'Georgia' => array('Atlanta', 'Augusta', 'Columbus', 'Savannah'),
    'North Carolina' => array('Charlotte', 'Raleigh', 'Greensboro', 'Durham'),
    'Michigan' => array('Detroit', 'Grand Rapids', 'Warren', 'Sterling Heights'),
    'Virginia' => array('Virginia Beach', 'Norfolk', 'Richmond', 'Newport News'),
    'Washington' => array('Seattle', 'Spokane', 'Tacoma', 'Vancouver'),
    'Arizona' => array('Phoenix', 'Tucson', 'Mesa', 'Chandler'),
    'Massachusetts' => array('Boston', 'Worcester', 'Springfield', 'Lowell'),
    'Tennessee' => array('Nashville', 'Memphis', 'Knoxville', 'Chattanooga'),
    'Indiana' => array('Indianapolis', 'Fort Wayne', 'Evansville', 'South Bend'),
    'Missouri' => array('Kansas City', 'St. Louis', 'Springfield', 'Columbia'),
    'Maryland' => array('Baltimore', 'Frederick', 'Rockville', 'Gaithersburg'),
    'Wisconsin' => array('Milwaukee', 'Madison', 'Green Bay', 'Kenosha'),
    'Colorado' => array('Denver', 'Colorado Springs', 'Aurora', 'Fort Collins'),
    'Minnesota' => array('Minneapolis', 'St. Paul', 'Rochester', 'Duluth'),
    'South Carolina' => array('Columbia', 'Charleston', 'North Charleston', 'Mount Pleasant'),
    'Louisiana' => array('New Orleans', 'Baton Rouge', 'Shreveport', 'Lafayette'),
    'Kentucky' => array('Louisville', 'Lexington', 'Bowling Green', 'Owensboro'),
    'Oregon' => array('Portland', 'Salem', 'Eugene', 'Gresham'),
    'Oklahoma' => array('Oklahoma City', 'Tulsa', 'Norman', 'Broken Arrow'),
    'Connecticut' => array('Bridgeport', 'New Haven', 'Hartford', 'Stamford'),
    'Utah' => array('Salt Lake City', 'West Valley City', 'Provo', 'West Jordan'),
    'Iowa' => array('Des Moines', 'Cedar Rapids', 'Davenport', 'Sioux City'),
    'Nevada' => array('Las Vegas', 'Henderson', 'Reno', 'North Las Vegas'),
    'Arkansas' => array('Little Rock', 'Fort Smith', 'Fayetteville', 'Springdale'),
    'Mississippi' => array('Jackson', 'Gulfport', 'Southaven', 'Hattiesburg'),
    'Kansas' => array('Wichita', 'Overland Park', 'Kansas City', 'Topeka'),
    'New Mexico' => array('Albuquerque', 'Las Cruces', 'Rio Rancho', 'Santa Fe'),
    'Nebraska' => array('Omaha', 'Lincoln', 'Bellevue', 'Grand Island'),
    'West Virginia' => array('Charleston', 'Huntington', 'Parkersburg', 'Morgantown'),
    'Idaho' => array('Boise', 'Meridian', 'Nampa', 'Idaho Falls'),
    'Hawaii' => array('Honolulu', 'Pearl City', 'Hilo', 'Kailua'),
    'New Hampshire' => array('Manchester', 'Nashua', 'Concord', 'Derry'),
    'Maine' => array('Portland', 'Lewiston', 'Bangor', 'South Portland'),
    'Montana' => array('Billings', 'Missoula', 'Great Falls', 'Bozeman'),
    'Rhode Island' => array('Providence', 'Warwick', 'Cranston', 'Pawtucket'),
    'Delaware' => array('Wilmington', 'Dover', 'Newark', 'Middletown'),
    'South Dakota' => array('Sioux Falls', 'Rapid City', 'Aberdeen', 'Brookings'),
    'North Dakota' => array('Fargo', 'Bismarck', 'Grand Forks', 'Minot'),
    'Alaska' => array('Anchorage', 'Fairbanks', 'Juneau', 'Sitka'),
    'Vermont' => array('Burlington', 'Essex', 'South Burlington', 'Colchester'),
    'Wyoming' => array('Cheyenne', 'Casper', 'Laramie', 'Gillette'),
    'District of Columbia' => array('Washington', 'Georgetown', 'Dupont Circle', 'Capitol Hill')
);

// Check which files already exist
$existing_files = array();
$theme_dir = __DIR__ . '/wp-content/themes/generatepress-child/';

foreach ($remaining_states as $state => $cities) {
    $state_slug = strtolower(str_replace(array(' ', '.'), array('-', ''), $state));
    $filename = "page-{$state_slug}.php";
    
    if (file_exists($theme_dir . $filename)) {
        $existing_files[] = $state;
    }
}

echo "📊 Status Check:\n";
echo "   - Total states to process: " . count($remaining_states) . "\n";
echo "   - Already exist: " . count($existing_files) . "\n";
echo "   - Need to create: " . (count($remaining_states) - count($existing_files)) . "\n\n";

if (!empty($existing_files)) {
    echo "✅ Already created: " . implode(', ', array_slice($existing_files, 0, 5));
    if (count($existing_files) > 5) {
        echo " and " . (count($existing_files) - 5) . " more";
    }
    echo "\n\n";
}

// Read the Alabama template
$alabama_file = $theme_dir . 'page-alabama.php';

if (!file_exists($alabama_file)) {
    die("❌ Error: Alabama template not found at: $alabama_file\n");
}

$template_content = file_get_contents($alabama_file);
echo "✅ Alabama template loaded (" . strlen($template_content) . " characters)\n\n";

// Helper functions
function state_to_slug($state) {
    return strtolower(str_replace(array(' ', '.'), array('-', ''), $state));
}

function state_to_var($state) {
    return strtolower(str_replace(array(' ', '.', '-'), '_', $state));
}

function state_to_camel($state) {
    return str_replace(array(' ', '-', '.'), '', ucwords($state, ' -.'));
}

// Process states in batches
$batch_size = 10;
$processed = 0;
$created_count = 0;
$skipped_count = 0;

echo "🔨 Processing states in batches of {$batch_size}...\n\n";

foreach ($remaining_states as $state => $cities) {
    if ($processed >= $batch_size) {
        echo "\n⏸️  Batch limit reached. Run script again to continue with remaining states.\n";
        break;
    }
    
    $state_slug = state_to_slug($state);
    $state_var = state_to_var($state);
    $state_camel = state_to_camel($state);
    
    $filename = "page-{$state_slug}.php";
    $filepath = $theme_dir . $filename;
    
    // Check if file already exists
    if (file_exists($filepath)) {
        echo "⏭️  Skipping $state - already exists\n";
        $skipped_count++;
        $processed++;
        continue;
    }
    
    echo "🔨 Creating: $state -> $filename\n";
    
    // Create state-specific content
    $state_content = $template_content;
    
    // Apply all the replacements
    $replacements = array(
        // Template header
        'Template Name: Alabama State Page' => "Template Name: {$state} State Page",
        'Custom Page Template for Alabama State Page' => "Custom Page Template for {$state} State Page",
        
        // Google Maps
        'callback=initAlabamaZoosMap' => "callback=init{$state_camel}ZoosMap",
        'initAlabamaZoosMap' => "init{$state_camel}ZoosMap",
        
        // State variables
        '$alabama_term = get_term_by(\'slug\', \'alabama\', \'location\');' => "\${$state_var}_term = get_term_by('slug', '{$state_slug}', 'location');",
        '$zoo_count = $alabama_term ? $alabama_term->count : 5;' => "\$zoo_count = \${$state_var}_term ? \${$state_var}_term->count : 5;",
        
        // Breadcrumbs and titles
        '<span class="current">Alabama</span>' => "<span class=\"current\">{$state}</span>",
        'Best Petting Zoos in Alabama' => "Best Petting Zoos in {$state}",
        
        // Section titles
        'All Petting Zoos in Alabama' => "All Petting Zoos in {$state}",
        'Best Petting Zoos in Alabama by Ranking' => "Best Petting Zoos in {$state} by Ranking",
        'Best Petting Zoos for Birthday Parties in Alabama' => "Best Petting Zoos for Birthday Parties in {$state}",
        'Best Mobile Petting Zoos in Alabama' => "Best Mobile Petting Zoos in {$state}",
        'Best Petting Zoos in Alabama with Bunnies' => "Best Petting Zoos in {$state} with Bunnies",
        'Best Indoor Petting Zoos in Alabama' => "Best Indoor Petting Zoos in {$state}",
        'Other Cities in Alabama' => "Other Cities in {$state}",
        
        // Content text
        'across Alabama' => "across {$state}",
        'in Alabama' => "in {$state}",
        'Alabama petting zoos' => "{$state} petting zoos",
        'throughout Alabama' => "throughout {$state}",
        'Find Your Perfect Petting Zoo Experience in Alabama' => "Find Your Perfect Petting Zoo Experience in {$state}",
        
        // Map and IDs
        'id="alabama-zoos-map"' => "id=\"{$state_slug}-zoos-map\"",
        'id="all-alabama-zoos"' => "id=\"all-{$state_slug}-zoos\"",
        
        // Taxonomy terms
        "'terms' => 'alabama'," => "'terms' => '{$state_slug}',",
        
        // Console logs
        'Alabama page content loaded' => "{$state} page content loaded",
        'Loading All Alabama Zoos section' => "Loading All {$state} Zoos section",
        'Loading Other Cities in Alabama section' => "Loading Other Cities in {$state} section",
        'Alabama Page:' => "{$state} Page:",
        
        // FAQ content
        'What are the most popular petting zoos in Alabama?' => "What are the most popular petting zoos in {$state}?",
        'Some of the most popular petting zoos in Alabama include' => "Some of the most popular petting zoos in {$state} include",
        'Are petting zoos in Alabama suitable for children' => "Are petting zoos in {$state} suitable for children",
        'petting zoos in Alabama are suitable for children' => "petting zoos in {$state} are suitable for children",
        'What should I bring to a petting zoo in Alabama?' => "What should I bring to a petting zoo in {$state}?",
        'When visiting a petting zoo in Alabama' => "When visiting a petting zoo in {$state}",
        
        // City names
        '$city_names = array(\'Birmingham\', \'Huntsville\', \'Mobile\', \'Montgomery\');' => '$city_names = array(\'' . implode('\', \'', $cities) . '\');',
    );
    
    foreach ($replacements as $search => $replace) {
        $state_content = str_replace($search, $replace, $state_content);
    }
    
    // Final cleanup - replace any remaining alabama/Alabama references
    $state_content = str_replace('alabama', $state_slug, $state_content);
    $state_content = str_replace('Alabama', $state, $state_content);
    
    // Write the file
    $result = file_put_contents($filepath, $state_content);
    
    if ($result !== false) {
        echo "✅ Created: $filename (" . number_format($result) . " bytes)\n";
        $created_count++;
    } else {
        echo "❌ Failed to create: $filename\n";
    }
    
    $processed++;
}

echo "\n🎉 Batch Processing Complete!\n";
echo "📊 This batch summary:\n";
echo "   - Processed: $processed states\n";
echo "   - Created: $created_count pages\n";
echo "   - Skipped: $skipped_count pages (already existed)\n";

$remaining_to_process = count($remaining_states) - $processed - count($existing_files);
if ($remaining_to_process > 0) {
    echo "   - Remaining: $remaining_to_process states\n";
    echo "\n🔄 Run this script again to process the next batch.\n";
}

echo "\n✨ State pages are ready to use!\n";
echo "\n💡 Next steps:\n";
echo "   1. Test the created pages in your browser\n";
echo "   2. Check console logs for any errors\n";
echo "   3. Verify all sections are working properly\n";
echo "   4. Run script again if more states need to be created\n";
?>
