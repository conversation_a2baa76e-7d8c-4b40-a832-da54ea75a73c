<!DOCTYPE html>
<html>
<head>
    <title>State Page Generator</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .button { background: #2c5530; color: white; padding: 12px 24px; border: none; border-radius: 6px; cursor: pointer; font-size: 16px; }
        .button:hover { background: #1e3a21; }
        .output { background: #f8f9fa; border: 1px solid #ddd; padding: 15px; margin-top: 20px; border-radius: 6px; white-space: pre-wrap; font-family: monospace; max-height: 500px; overflow-y: auto; }
        .status { padding: 10px; margin: 10px 0; border-radius: 4px; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏛️ State Page Generator</h1>
        <p>This tool will generate individual state page templates based on the Alabama template structure.</p>
        
        <?php if (isset($_POST['generate'])): ?>
            <div class="status success">
                <strong>✅ Generation Started</strong><br>
                Processing all states... Please wait.
            </div>
            
            <div class="output">
<?php
// Set up WordPress-like environment
define('ABSPATH', dirname(__FILE__) . '/');

// Capture output
ob_start();

try {
    // Include and run the generator
    include 'generate-state-pages.php';
    $output = ob_get_contents();
} catch (Exception $e) {
    $output = "❌ Error: " . $e->getMessage();
} finally {
    ob_end_clean();
}

echo htmlspecialchars($output);
?>
            </div>
            
            <div style="margin-top: 20px;">
                <a href="?" class="button">🔄 Run Again</a>
                <a href="wp-content/themes/generatepress-child/" class="button" style="background: #6c757d;">📁 View Generated Files</a>
            </div>
            
        <?php else: ?>
            
            <div class="status warning">
                <strong>⚠️ Important Notes:</strong><br>
                • This will create page templates for all US states (except Alabama which already exists)<br>
                • Files will be created in wp-content/themes/generatepress-child/<br>
                • Existing files will be skipped to prevent overwriting<br>
                • Make sure you have proper file permissions
            </div>
            
            <form method="post">
                <button type="submit" name="generate" class="button">
                    🚀 Generate All State Pages
                </button>
            </form>
            
            <h3>📋 What this will create:</h3>
            <ul>
                <li>Individual PHP templates for each state (e.g., page-california.php, page-texas.php)</li>
                <li>Hero sections with state-specific titles and breadcrumbs</li>
                <li>All 8 required H2/H3 sections with state-specific content</li>
                <li>Google Maps integration for each state</li>
                <li>Comprehensive logging and debugging features</li>
                <li>FAQ sections with state-specific questions</li>
                <li>Proper styling matching the Alabama template</li>
            </ul>
            
            <h3>🔧 Technical Details:</h3>
            <ul>
                <li>Reads state data from: wp-content/uploads/2025/06/city-state.csv</li>
                <li>Based on template: wp-content/themes/generatepress-child/page-alabama.php</li>
                <li>Creates files with naming pattern: page-{state-slug}.php</li>
                <li>Includes proper WordPress template headers</li>
                <li>Maintains all debugging and console logging features</li>
            </ul>
            
        <?php endif; ?>
    </div>
</body>
</html>
