<?php
/**
 * Test State Page Generator
 * Quick test to generate a few state pages
 */

// Set up environment
define('ABSPATH', __DIR__ . '/');

echo "🧪 Testing State Page Generator...\n";

// Read CSV file to get states
$csv_file = ABSPATH . 'wp-content/uploads/2025/06/city-state.csv';
$states = array();

if (!file_exists($csv_file)) {
    die("❌ Error: CSV file not found at: $csv_file\n");
}

echo "📊 Reading CSV file: $csv_file\n";

$handle = fopen($csv_file, 'r');
if ($handle !== FALSE) {
    $header = fgetcsv($handle); // Skip header row
    echo "📋 CSV Header: " . implode(', ', $header) . "\n";
    
    while (($data = fgetcsv($handle)) !== FALSE) {
        if (count($data) >= 3 && !empty($data[2])) {
            $state = trim($data[2]);
            if (!in_array($state, $states)) {
                $states[] = $state;
            }
        }
    }
    fclose($handle);
}

// Remove Alabama since it already exists
$states = array_filter($states, function($state) {
    return $state !== 'Alabama';
});

sort($states);

echo "📋 Found " . count($states) . " states to process (excluding Alabama)\n";
echo "First 10 states: " . implode(', ', array_slice($states, 0, 10)) . "\n\n";

// Test with just a few states first
$test_states = array_slice($states, 0, 3);
echo "🧪 Testing with: " . implode(', ', $test_states) . "\n\n";

// Read the Alabama template
$alabama_template = ABSPATH . 'wp-content/themes/generatepress-child/page-alabama.php';
if (!file_exists($alabama_template)) {
    die("❌ Error: Alabama template not found at: $alabama_template\n");
}

$template_content = file_get_contents($alabama_template);
echo "✅ Alabama template loaded (" . strlen($template_content) . " characters)\n";

// Function to convert state name to slug
function state_to_slug($state) {
    return strtolower(str_replace(array(' ', '.'), array('-', ''), $state));
}

// Function to convert state name to variable name
function state_to_var($state) {
    return strtolower(str_replace(array(' ', '.', '-'), '_', $state));
}

// Process test states
$created_count = 0;

foreach ($test_states as $state) {
    $state_slug = state_to_slug($state);
    $state_var = state_to_var($state);
    
    $filename = "page-{$state_slug}.php";
    $filepath = ABSPATH . "wp-content/themes/generatepress-child/{$filename}";
    
    echo "🔨 Creating test page for: $state -> $filename\n";
    
    // Create state-specific content by replacing Alabama references
    $state_content = $template_content;
    
    // Basic replacements
    $state_content = str_replace('Alabama', $state, $state_content);
    $state_content = str_replace('alabama', $state_slug, $state_content);
    
    // Replace template name
    $state_content = str_replace(
        'Template Name: Alabama State Page',
        "Template Name: {$state} State Page",
        $state_content
    );
    
    // Write the file
    $result = file_put_contents($filepath, $state_content);
    
    if ($result !== false) {
        echo "✅ Created: $filename (" . number_format($result) . " bytes)\n";
        $created_count++;
    } else {
        echo "❌ Failed to create: $filename\n";
    }
}

echo "\n🎉 Test Complete!\n";
echo "📊 Created: $created_count test pages\n";

if ($created_count > 0) {
    echo "\n🔧 Generated test files:\n";
    foreach ($test_states as $state) {
        $state_slug = state_to_slug($state);
        $filename = "page-{$state_slug}.php";
        echo "   - $filename\n";
    }
}

echo "\n✨ Test completed successfully!\n";
?>
