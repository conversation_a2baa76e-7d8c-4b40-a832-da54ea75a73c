<?php
/**
 * Full State Page Generator
 * Generates all state page templates based on the Alabama template
 */

// Set content type for web output
if (isset($_SERVER['HTTP_HOST'])) {
    header('Content-Type: text/plain; charset=utf-8');
}

define('ABSPATH', __DIR__ . '/');

echo "🚀 Starting Full State Page Generator...\n";

// Read CSV file to get all states
$csv_file = ABSPATH . 'wp-content/uploads/2025/06/city-state.csv';
$states = array();

if (!file_exists($csv_file)) {
    die("❌ Error: CSV file not found at: $csv_file\n");
}

echo "📊 Reading CSV file: $csv_file\n";

$handle = fopen($csv_file, 'r');
if ($handle !== FALSE) {
    $header = fgetcsv($handle);
    echo "📋 CSV Header: " . implode(', ', $header) . "\n";
    
    while (($data = fgetcsv($handle)) !== FALSE) {
        if (count($data) >= 3 && !empty($data[2])) {
            $state = trim($data[2]);
            if (!in_array($state, $states)) {
                $states[] = $state;
            }
        }
    }
    fclose($handle);
}

// Remove Alabama since it already exists
$states = array_filter($states, function($state) {
    return $state !== 'Alabama';
});

sort($states);

echo "📋 Found " . count($states) . " states to process (excluding Alabama)\n";
echo "States: " . implode(', ', array_slice($states, 0, 10)) . (count($states) > 10 ? '...' : '') . "\n\n";

// Read the Alabama template
$alabama_template = ABSPATH . 'wp-content/themes/generatepress-child/page-alabama.php';
if (!file_exists($alabama_template)) {
    die("❌ Error: Alabama template not found at: $alabama_template\n");
}

$template_content = file_get_contents($alabama_template);
echo "✅ Alabama template loaded (" . strlen($template_content) . " characters)\n";

// Helper functions
function state_to_slug($state) {
    return strtolower(str_replace(array(' ', '.'), array('-', ''), $state));
}

function state_to_var($state) {
    return strtolower(str_replace(array(' ', '.', '-'), '_', $state));
}

function state_to_camel($state) {
    return str_replace(array(' ', '-', '.'), '', ucwords($state, ' -.'));
}

// Process each state
$created_count = 0;
$skipped_count = 0;

foreach ($states as $state) {
    $state_slug = state_to_slug($state);
    $state_var = state_to_var($state);
    $state_camel = state_to_camel($state);
    
    $filename = "page-{$state_slug}.php";
    $filepath = ABSPATH . "wp-content/themes/generatepress-child/{$filename}";
    
    // Check if file already exists
    if (file_exists($filepath)) {
        echo "⏭️  Skipping $state - file already exists: $filename\n";
        $skipped_count++;
        continue;
    }
    
    echo "🔨 Creating page for: $state -> $filename\n";
    
    // Create state-specific content
    $state_content = $template_content;
    
    // Replace template header
    $state_content = str_replace(
        'Template Name: Alabama State Page',
        "Template Name: {$state} State Page",
        $state_content
    );
    
    $state_content = str_replace(
        'Custom Page Template for Alabama State Page',
        "Custom Page Template for {$state} State Page",
        $state_content
    );
    
    // Replace Google Maps callback
    $state_content = str_replace(
        'callback=initAlabamaZoosMap',
        "callback=init{$state_camel}ZoosMap",
        $state_content
    );
    
    // Replace state term variable
    $state_content = str_replace(
        '$alabama_term = get_term_by(\'slug\', \'alabama\', \'location\');',
        "\${$state_var}_term = get_term_by('slug', '{$state_slug}', 'location');",
        $state_content
    );
    
    $state_content = str_replace(
        '$zoo_count = $alabama_term ? $alabama_term->count : 5;',
        "\$zoo_count = \${$state_var}_term ? \${$state_var}_term->count : 5;",
        $state_content
    );
    
    // Replace breadcrumbs
    $state_content = str_replace(
        '<span class="current">Alabama</span>',
        "<span class=\"current\">{$state}</span>",
        $state_content
    );
    
    // Replace page title
    $state_content = str_replace(
        'Best Petting Zoos in Alabama',
        "Best Petting Zoos in {$state}",
        $state_content
    );
    
    // Replace all section titles
    $replacements = array(
        'All Petting Zoos in Alabama' => "All Petting Zoos in {$state}",
        'Best Petting Zoos in Alabama by Ranking' => "Best Petting Zoos in {$state} by Ranking",
        'Best Petting Zoos for Birthday Parties in Alabama' => "Best Petting Zoos for Birthday Parties in {$state}",
        'Best Mobile Petting Zoos in Alabama' => "Best Mobile Petting Zoos in {$state}",
        'Best Petting Zoos in Alabama with Bunnies' => "Best Petting Zoos in {$state} with Bunnies",
        'Best Indoor Petting Zoos in Alabama' => "Best Indoor Petting Zoos in {$state}",
        'Other Cities in Alabama' => "Other Cities in {$state}",
        'across Alabama' => "across {$state}",
        'in Alabama' => "in {$state}",
        'Alabama petting zoos' => "{$state} petting zoos",
        'throughout Alabama' => "throughout {$state}",
        'across the state' => "across {$state}",
    );
    
    foreach ($replacements as $search => $replace) {
        $state_content = str_replace($search, $replace, $state_content);
    }
    
    // Replace map container ID
    $state_content = str_replace(
        'id="alabama-zoos-map"',
        "id=\"{$state_slug}-zoos-map\"",
        $state_content
    );
    
    // Replace function names
    $state_content = str_replace(
        'initAlabamaZoosMap',
        "init{$state_camel}ZoosMap",
        $state_content
    );
    
    // Replace console log messages
    $log_replacements = array(
        'Alabama page content loaded' => "{$state} page content loaded",
        'Loading All Alabama Zoos section' => "Loading All {$state} Zoos section",
        'Loading Other Cities in Alabama section' => "Loading Other Cities in {$state} section",
        'Alabama Page:' => "{$state} Page:",
    );
    
    foreach ($log_replacements as $search => $replace) {
        $state_content = str_replace($search, $replace, $state_content);
    }
    
    // Replace FAQ content
    $faq_replacements = array(
        'What are the most popular petting zoos in Alabama?' => "What are the most popular petting zoos in {$state}?",
        'Some of the most popular petting zoos in Alabama include' => "Some of the most popular petting zoos in {$state} include",
        'Are petting zoos in Alabama suitable for children' => "Are petting zoos in {$state} suitable for children",
        'petting zoos in Alabama are suitable for children' => "petting zoos in {$state} are suitable for children",
        'What should I bring to a petting zoo in Alabama?' => "What should I bring to a petting zoo in {$state}?",
        'When visiting a petting zoo in Alabama' => "When visiting a petting zoo in {$state}",
    );
    
    foreach ($faq_replacements as $search => $replace) {
        $state_content = str_replace($search, $replace, $state_content);
    }
    
    // Replace remaining alabama/Alabama references
    $state_content = str_replace('alabama', $state_slug, $state_content);
    $state_content = str_replace('Alabama', $state, $state_content);
    
    // Write the file
    $result = file_put_contents($filepath, $state_content);
    
    if ($result !== false) {
        echo "✅ Created: $filename (" . number_format($result) . " bytes)\n";
        $created_count++;
    } else {
        echo "❌ Failed to create: $filename\n";
    }
}

echo "\n🎉 State Page Generation Complete!\n";
echo "📊 Summary:\n";
echo "   - Created: $created_count pages\n";
echo "   - Skipped: $skipped_count pages (already existed)\n";
echo "   - Total states processed: " . count($states) . "\n";

if ($created_count > 0) {
    echo "\n🔧 Generated files:\n";
    $generated_files = array();
    foreach ($states as $state) {
        $state_slug = state_to_slug($state);
        $filename = "page-{$state_slug}.php";
        $filepath = ABSPATH . "wp-content/themes/generatepress-child/{$filename}";
        
        if (file_exists($filepath)) {
            $generated_files[] = $filename;
        }
    }
    
    foreach (array_slice($generated_files, 0, 20) as $file) {
        echo "   - $file\n";
    }
    
    if (count($generated_files) > 20) {
        echo "   ... and " . (count($generated_files) - 20) . " more files\n";
    }
}

echo "\n💡 Next steps:\n";
echo "   1. Review the generated pages in wp-content/themes/generatepress-child/\n";
echo "   2. Test a few pages to ensure proper functionality\n";
echo "   3. Check console logs for any errors\n";
echo "   4. Verify Google Maps integration works for each state\n";
echo "\n✨ All done! Your state pages are ready to use.\n";
?>
